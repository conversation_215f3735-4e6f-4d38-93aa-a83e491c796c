import { useTranslation } from "react-i18next";

function LanguageSwitcher() {
  const { i18n } = useTranslation();

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng); 
  };

  return (
    <div className="w-20 bg-white rounded-md">
      <button className="w-full cursor-pointer" onClick={() => changeLanguage("en")}>EN</button>
      <button className="w-full cursor-pointer" onClick={() => changeLanguage("ar")}>AR</button>
    </div>
  );
}

export default LanguageSwitcher;
