{"name": "server", "version": "1.0.0", "description": "", "main": "index.js", "nodemonConfig": {"watch": ["src"], "ext": ".ts,.js", "exec": "npx ts-node ./src/index.ts"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon"}, "author": "", "license": "ISC", "dependencies": {"cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "mongoose": "^8.18.0", "nodemon": "^3.1.10", "winston": "^3.17.0"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^24.3.0", "@types/nodemon": "^3.1.1", "ts-node-dev": "^2.0.0", "typescript": "^5.9.2"}}