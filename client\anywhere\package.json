{"name": "anywhere", "private": true, "version": "0.0.0", "type": "module", "scripts": {"test": "vitest", "test:ui": "vitest --ui", "coverage": "vitest run --coverage", "dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.3.1", "@mui/material": "^7.3.1", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/vite": "^4.1.12", "i18next": "^25.4.2", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "react": "^19.1.1", "react-dom": "^19.1.1", "react-i18next": "^15.7.3", "react-redux": "^9.2.0", "react-router-dom": "^7.8.2", "redux-persist": "^6.0.0", "tailwindcss": "^4.1.12"}, "devDependencies": {"@eslint/js": "^9.33.0", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/react": "^19.1.11", "@types/react-dom": "^19.1.8", "@vitejs/plugin-react": "^5.0.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "jsdom": "^26.1.0", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2", "vitest": "^3.2.4"}}